<template>
  <div class="polling-container">
    <!-- 自定义标签切换器 -->
    <div class="tab-switcher" v-if="!systemIssue">
      <div class="tab-container">
        <div v-for="(tab, index) in list" :key="index" class="tab-item" :class="{ active: current === tab }" @click="switchTab(tab)">
          <div class="tab-icon">
            <wd-icon :name="getTabIcon(tab)" size="18px" />
          </div>
          <span class="tab-text">{{ tab }}</span>
          <div class="tab-badge" v-if="getTabCount(tab) > 0">{{ getTabCount(tab) }}</div>
        </div>
        <div class="tab-indicator" :style="{ transform: `translateX(${getIndicatorPosition()}%)` }"></div>
      </div>
    </div>

    <!-- 任务列表内容 -->
    <div class="content-section">
      <scroll-view class="scroll-container" scroll-y="true" v-if="taskList.length > 0">
        <div class="task-list">
          <template v-for="(item, index) in taskList" :key="index">
            <TaskCard :isShow="systemIssue" :item="item" :is-completed="current === '已结束任务'" @click="handleItemClick" />
          </template>
        </div>
      </scroll-view>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <div class="empty-icon">
          <wd-icon name="list" size="48px" color="#cccccc" />
        </div>
        <h3 class="empty-text">暂无任务数据</h3>
        <p class="empty-hint">{{ current === '已结束任务' ? '暂无已结束的任务' : '暂无待核查的任务' }}</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="total > 0">
      <wd-pagination v-model="params.page" :total="total" :pageSize="params.pageSize" @change="handlePaginationChange" show-icon custom-class="modern-pagination" />
    </div>
  </div>

  <FloatingButton draggable v-if="systemIssue" :size="80" icon="move" background-color="#33aa71" :initial-position="{ x: 300, y: 500 }" :bounds="{ top: 100, right: 50, bottom: 100, left: 50 }" @click="handleMoveClick">
    <wd-icon name="add" size="24px" color="#ffffff"></wd-icon>
  </FloatingButton>

  <wd-popup v-model="pumpHouseOpen" closable position="bottom" custom-class="u2-popup z-index-999" @close="handleClosePumpHouse">
    <TaskCreate :data="pumpHouseField" v-if="pumpHouseOpen" @finished="handleCreateFinished" />
  </wd-popup>
  <wd-toast />
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { onShow, onShareAppMessage } from '@dcloudio/uni-app'
import { PumpHouseApi } from '@/services/model/pump.house'
import * as CommonApi from '@/services/model/common.js'
import { useToast } from 'wot-design-uni'
import { cache } from '@/utils/cache.js'
import TaskCard from './components/TaskCard/index.vue'
import FloatingButton from '@/components/FloatingButton/index.vue'
import TaskCreate from './components/TaskCreate/index.vue'
import moment from 'moment'

// 设置 moment 中文本地化
moment.locale('zh-cn')

const toast = useToast()
const taskList = ref([])
const total = ref(0)
const list = ref(['待核查任务', '已结束任务'])
const current = ref('待核查任务')
const systemUserIds = ref([])
const userInfo = cache.get('userInfo')
const systemIssue = computed(() => systemUserIds.value.includes(userInfo.id))

// 任务统计数据
const taskStats = ref({ pending: 0, completed: 0 })

// 分页参数
const params = reactive({ page: 1, pageSize: 5 })

onShow(async () => {
  // 重置分页参数
  params.page = 1

  systemUserIds.value = await getSystem()
  if (systemUserIds.value.includes(userInfo.id)) {
    getTaskList()
  } else {
    pendingList()
  }
})

async function getTaskList() {
  try {
    toast.loading('正在加载...')
    const { data, pagination } = await PumpHouseApi.taskList(params)
    taskList.value = data || []
    total.value = pagination?.total || 0
    toast.close()
  } catch (error) {
    console.error('获取任务列表失败:', error)
    toast.error('获取任务列表失败')
    taskList.value = []
    total.value = 0
    toast.close()
  }
}

async function pendingList() {
  try {
    // 切换标签时重置到第一页
    params.page = 1

    toast.loading('正在加载...')
    const end = current.value === '已结束任务' ? 1 : 0
    const requestParams = { ...params, end }
    const { data, pagination } = await PumpHouseApi.pendingList(requestParams)
    taskList.value = data || []
    total.value = pagination?.total || 0

    // 更新统计数据
    if (current.value === '待核查任务') {
      taskStats.value.pending = total.value
    } else {
      taskStats.value.completed = total.value
    }

    toast.close()
  } catch (error) {
    console.error('获取任务列表失败:', error)
    toast.error('获取任务列表失败')
    taskList.value = []
    total.value = 0
    toast.close()
  }
}

// 分页处理
function handlePaginationChange({ value }) {
  params.page = value
  if (systemUserIds.value.includes(userInfo.id)) {
    getTaskList()
  } else {
    pendingList()
  }
}

// 点击任务项
function handleItemClick(item) {
  uni.vibrateShort({ type: 'medium' })
  // 这里可以跳转到任务详情页面
  if (systemIssue.value) {
    uni.navigateTo({ url: `/pages/pump-house/examineStatistics?id=${item.TaskID}` })
  } else {
    uni.navigateTo({ url: `/pages/pump-house/examineList?id=${item.TaskID}` })
  }

  // uni.navigateTo({ url: `/pages/task/detail?id=${item.id}` })
}

// 标签切换
function switchTab(tab) {
  current.value = tab
  pendingList()
}

// 获取标签图标
function getTabIcon(tab) {
  return tab === '待核查任务' ? 'clock' : 'check-circle'
}

// 获取标签数量
function getTabCount(tab) {
  if (tab === '待核查任务') {
    return taskStats.value.pending
  }
  return taskStats.value.completed
}

// 获取指示器位置
function getIndicatorPosition() {
  const index = list.value.indexOf(current.value)
  return index * 100 // 每个标签宽度的百分比
}

// 获取字典数据 记录了哪些用户能
async function getSystem() {
  const { data } = await CommonApi.dictionaryLookup('system:patrol:issue')
  const systemUserIds = data.map((item) => Number(item.DictCode))
  // systemUserIds.push(5) // 模拟添加权限用户
  return systemUserIds
}

const pumpHouseOpen = ref(false)
const pumpHouseField = ref(null)
async function handleMoveClick() {
  uni.vibrateShort({ type: 'medium' })
  if (pumpHouseOpen.value) return (pumpHouseOpen.value = false)
  const { data } = await CommonApi.dictionaryLookup('pump_house_field') //获取二供字段接口
  pumpHouseField.value = data.map((item) => ({ key: item.DictCode, value: item.DictValue }))
  console.log(pumpHouseField.value)

  pumpHouseOpen.value = true
}

function handleCreateFinished() {
  pumpHouseOpen.value = false
  pendingList()
}

onShareAppMessage(({ target }) => {
  uni.vibrateShort({ type: 'medium' })
  const TaskID = target.dataset.id
  return { title: `核查任务`, path: `/pages/pump-house/examineList?id=${TaskID}` }
})
</script>

<style lang="less" scoped>
.polling-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24rpx;
  position: relative;
  overflow: hidden;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 20s infinite linear;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 20%;
    right: -30%;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    animation: float 15s infinite linear reverse;
    pointer-events: none;
  }
}

@keyframes float {
  0% {
    transform: rotate(0deg) translate(-20px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translate(-20px) rotate(-360deg);
  }
}

// 自定义标签切换器样式
.tab-switcher {
  margin-bottom: 24rpx;
}

.tab-container {
  position: relative;
  display: flex;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 24rpx;
  padding: 8rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.tab-item {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  border-radius: 20rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;

  &.active {
    color: #2d3748;
    font-weight: 600;

    .tab-icon {
      color: #4299e1;
    }
  }

  &:not(.active) {
    color: rgba(255, 255, 255, 0.8);

    &:hover {
      color: rgba(255, 255, 255, 0.95);
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.tab-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border-radius: 18rpx;
  font-size: 20rpx;
  font-weight: 600;
  padding: 0 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.tab-indicator {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: calc(50% - 8rpx);
  height: calc(100% - 16rpx);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 1;
}

// 内容区域样式
.content-section {
  flex: 1;
  margin-top: 24rpx;
  overflow: hidden;
}

.scroll-container {
  height: 100%;
  width: 100%;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding-bottom: 20rpx;
}

// 卡片相关样式已移到 TaskCard 组件中

// 所有卡片详情样式已移到 TaskCard 组件中

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  position: relative;
}

.empty-icon {
  margin-bottom: 32rpx;
  opacity: 0.7;
  animation: float-gentle 3s ease-in-out infinite;

  // 添加发光效果
  filter: drop-shadow(0 0 20rpx rgba(255, 255, 255, 0.3));
}

@keyframes float-gentle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.empty-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #fff;
  margin: 0 0 16rpx 0;
  line-height: 1.3;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  animation: fade-in-up 0.6s ease-out;
}

.empty-hint {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  animation: fade-in-up 0.8s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 分页样式
.pagination-section {
  padding: 16rpx 0;
  background: transparent;
  flex-shrink: 0;
}

// 自定义分页样式
:deep(.modern-pagination) {
  .wd-pagination {
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 16rpx !important;
    padding: 16rpx !important;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
  }
}

/* 分页样式 - 小程序兼容写法 */
.modern-pagination .wd-pagination {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 16rpx !important;
  padding: 16rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
}

/* 全局分页样式覆盖 */
.wd-pagination {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 16rpx !important;
  padding: 16rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
}

// 响应式设计
@media (max-width: 750rpx) {
  .polling-container {
    padding: 16rpx;
  }
}
</style>

<style lang="less">
.u2-popup {
  .wd-popup__close {
    color: #3a3a3a !important;
  }
}
</style>

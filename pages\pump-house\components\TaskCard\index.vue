<template>
  <div class="task-card" @click="handleClick">
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="header-left">
        <div class="header-icon">
          <wd-icon name="task" size="20px" color="#4f46e5" />
        </div>
        <div class="header-text">
          <h3 class="card-title">{{ item.TaskRemark || '核查任务' }}</h3>
          <p class="card-subtitle" v-if="item.PumpHouseName">{{ item.PumpHouseName }}</p>
        </div>
      </div>
      <div class="status-badge" :class="statusClass">
        <span>{{ statusText }}</span>
      </div>
      <button style="padding: 0" class="mar-L16" v-if="isShow" :data-id="item.TaskID" open-type="share" @click.stop="">
        <div class="share-icon-wrapper">
          <wd-icon name="share" size="24px" color="#fff"></wd-icon>
        </div>
      </button>
    </div>

    <div class="card-body">
      <!-- 任务基本信息 -->
      <div class="task-info-section">
        <div class="info-row">
          <div class="info-item" v-if="item.TaskID">
            <div class="info-label">
              <wd-icon name="edit" size="14px" color="#6b7280" />
              <span>任务ID</span>
            </div>
            <div class="info-value">{{ item.TaskID }}</div>
          </div>

          <div class="info-item" v-if="item.TaskEndTime">
            <div class="info-label">
              <wd-icon name="time" size="14px" color="#6b7280" />
              <span>{{ isCompleted ? '结束时间' : '截止时间' }}</span>
            </div>
            <div class="info-value" :class="{ countdown: !isCompleted && isOverdue }">
              {{ isCompleted ? formatDateTime(item.TaskEndTime) : getCountdown(item.TaskEndTime) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 总体进度 -->
      <div class="overall-progress-section" v-if="hasProgress">
        <div class="progress-header">
          <div class="progress-title">总体完成进度</div>
          <div class="progress-stats">
            <span class="progress-percentage">{{ progressPercentage }}%</span>
            <span class="progress-detail" v-if="progressDetail"> {{ progressDetail }} </span>
          </div>
        </div>

        <!-- 线性进度条 -->
        <div class="linear-progress-container">
          <div class="progress-track">
            <div class="progress-fill" :style="{ width: progressPercentage + '%' }" :class="progressClass">
              <div class="progress-shine"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import moment from 'moment'

// 设置 moment 中文本地化
moment.locale('zh-cn')

const props = defineProps({
  item: { type: Object, required: true },
  isCompleted: { type: Boolean, default: false },
  isShow: { type: Boolean, default: false }
})

const emit = defineEmits(['click'])

// 统一的时间处理函数
function parseTimeWithTimezone(timeString) {
  if (!timeString) return null
  try {
    const momentDate = moment(timeString)
    if (!momentDate.isValid()) return null
    // 使用 moment 处理时区，确保时间正确
    return momentDate.utcOffset(8) // 设置为东八区
  } catch (error) {
    console.warn('parseTimeWithTimezone error:', error)
    return null
  }
}

// 判断任务是否逾期
function isTaskOverdue(endTime) {
  const end = parseTimeWithTimezone(endTime)
  if (!end) return false

  const now = moment()
  return now.isAfter(end)
}

// 获取倒计时文本
function getCountdown(endTime) {
  const end = parseTimeWithTimezone(endTime)
  if (!end) return ''

  const now = moment()
  const diff = end.diff(now)

  if (diff <= 0) {
    return '已结束'
  }

  const duration = moment.duration(diff)
  const days = Math.floor(duration.asDays())
  const hours = duration.hours()
  const minutes = duration.minutes()

  if (days > 0) {
    return `剩余 ${days} 天 ${hours} 小时`
  } else if (hours > 0) {
    return `剩余 ${hours} 小时 ${minutes} 分钟`
  } else if (minutes > 0) {
    return `剩余 ${minutes} 分钟`
  } else {
    return '即将到期'
  }
}

// 格式化时间
function formatTime(time) {
  const date = parseTimeWithTimezone(time)
  if (!date) return ''

  const now = moment()
  const diff = now.diff(date)

  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.format('YYYY-MM-DD HH:mm')
  }
}

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '--'
  try {
    const momentDate = moment(dateTime)
    if (!momentDate.isValid()) return dateTime
    return momentDate.format('YYYY-MM-DD HH:mm')
  } catch (error) {
    console.warn('formatDateTime error:', error)
    return dateTime
  }
}

// 计算属性
const isOverdue = computed(() => {
  return !props.isCompleted && props.item.TaskEndTime && isTaskOverdue(props.item.TaskEndTime)
})

const statusClass = computed(() => {
  if (props.isCompleted) return 'status-completed'
  if (isOverdue.value) return 'status-overdue'
  return 'status-pending'
})

const statusIcon = computed(() => {
  if (props.isCompleted) return 'check-circle'
  if (isOverdue.value) return 'warn'
  return 'time'
})

const statusText = computed(() => {
  if (props.isCompleted) return '已结束'
  if (isOverdue.value) return '已结束'
  return '进行中'
})

// 进度相关计算属性
const hasProgress = computed(() => {
  return props.item.inspectionProgress && typeof props.item.inspectionProgress === 'object'
})

const progressPercentage = computed(() => {
  if (!hasProgress.value) return 0

  const progress = props.item.inspectionProgress

  if (progress.progressPercentage !== undefined) {
    return Math.round(progress.progressPercentage)
  }

  if (progress.totalPumpRooms && progress.completedPumpRooms !== undefined) {
    const total = parseInt(progress.totalPumpRooms) || 0
    const completed = parseInt(progress.completedPumpRooms) || 0
    return total > 0 ? Math.round((completed / total) * 100) : 0
  }

  return 0
})

const progressDetail = computed(() => {
  if (!hasProgress.value) return ''

  const progress = props.item.inspectionProgress
  if (progress.totalPumpRooms !== undefined && progress.completedPumpRooms !== undefined) {
    return `${progress.completedPumpRooms}/${progress.totalPumpRooms}`
  }

  return ''
})

const progressStatus = computed(() => {
  const percentage = progressPercentage.value
  if (percentage >= 100) return '已结束'
  if (percentage >= 80) return '接近结束'
  if (percentage >= 50) return '进行中'
  if (percentage > 0) return '刚开始'
  return '未开始'
})

const progressClass = computed(() => {
  const percentage = progressPercentage.value
  if (percentage >= 100) return 'progress-completed'
  if (percentage >= 80) return 'progress-nearly-done'
  if (percentage >= 50) return 'progress-in-progress'
  if (percentage > 0) return 'progress-started'
  return 'progress-not-started'
})

// 事件处理
function handleClick() {
  emit('click', props.item)
}
</script>

<style lang="less" scoped>
.task-card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 32rpx;
  overflow: hidden;
  border: 1rpx solid #e5e7eb;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f3f4f6;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .header-icon {
      width: 48rpx;
      height: 48rpx;
      background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .header-text {
      .card-title {
        font-size: 28rpx;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 4rpx 0;
      }

      .card-subtitle {
        font-size: 22rpx;
        color: #6b7280;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 1rpx;
      }
    }
  }

  .status-badge {
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    font-weight: 600;

    &.status-pending {
      background: #dbeafe;
      color: #2563eb;
    }

    &.status-completed {
      background: #f3f4f6;
      color: #6b7280;
    }

    &.status-overdue {
      background: #f3f4f6;
      color: #6b7280;
    }
  }
}

.share-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4), 0 2rpx 8rpx rgba(118, 75, 162, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-body {
  padding: 32rpx;

  // 任务信息部分
  .task-info-section {
    margin-bottom: 32rpx;

    .info-row {
      display: flex;
      gap: 24rpx;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-item {
        flex: 1;

        .info-label {
          display: flex;
          align-items: center;
          gap: 8rpx;
          font-size: 22rpx;
          color: #6b7280;
          margin-bottom: 8rpx;
          font-weight: 500;
        }

        .info-value {
          font-size: 24rpx;
          color: #1f2937;
          font-weight: 600;
          line-height: 140%;

          &.countdown {
            color: #dc2626;
            animation: pulse 2s infinite;
          }
        }
      }
    }
  }

  // 总体进度部分
  .overall-progress-section {
    .progress-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .progress-title {
        font-size: 26rpx;
        font-weight: 700;
        color: #1f2937;
      }

      .progress-stats {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .progress-percentage {
          font-size: 28rpx;
          font-weight: 700;
          color: #4f46e5;
        }

        .progress-detail {
          font-size: 22rpx;
          color: #6b7280;
        }
      }
    }

    .linear-progress-container {
      margin-bottom: 24rpx;

      .progress-track {
        height: 12rpx;
        background: #f3f4f6;
        border-radius: 6rpx;
        overflow: hidden;
        position: relative;

        .progress-fill {
          height: 100%;
          border-radius: 6rpx;
          position: relative;
          transition: width 0.8s ease;
          overflow: hidden;

          .progress-shine {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shine 2s infinite;
          }

          &.progress-completed {
            background: linear-gradient(90deg, #10b981, #059669);
          }

          &.progress-nearly-done {
            background: linear-gradient(90deg, #f59e0b, #d97706);
          }

          &.progress-in-progress {
            background: linear-gradient(90deg, #4f46e5, #3730a3);
          }

          &.progress-started {
            background: linear-gradient(90deg, #7c3aed, #5b21b6);
          }

          &.progress-not-started {
            background: linear-gradient(90deg, #ef4444, #dc2626);
          }
        }
      }
    }
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .card-header {
    padding: 24rpx;
  }

  .card-body {
    padding: 24rpx;
  }

  .header-text {
    .card-title {
      font-size: 26rpx;
    }

    .card-subtitle {
      font-size: 20rpx;
    }
  }

  .status-badge {
    font-size: 20rpx;
    padding: 6rpx 12rpx;
  }

  .info-label {
    font-size: 20rpx;
  }

  .info-value {
    font-size: 22rpx;
  }

  .progress-title {
    font-size: 24rpx;
  }

  .progress-percentage {
    font-size: 26rpx;
  }

  .progress-detail {
    font-size: 20rpx;
  }
}
</style>

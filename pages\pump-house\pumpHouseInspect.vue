<template>
  <div class="all flex f-column">
    <!-- 核查记录展示页面 -->
    <div v-if="isInspected" class="inspection-record-view">
      <!-- 未核查状态页面 -->
      <div v-if="isNotInspected" class="not-inspected-view">
        <!-- 页面头部 -->
        <view class="not-inspected-header">
          <view class="header-content">
            <view class="header-icon not-inspected">
              <wd-icon name="info" size="32rpx" color="#faad14" />
            </view>
            <view class="header-info">
              <text class="header-title">核查状态</text>
              <text class="header-subtitle">该泵房本次任务未进行核查</text>
            </view>
            <view class="status-badge not-inspected">
              <wd-icon name="clock" size="16rpx" color="#ffffff" />
              <text class="status-text">未核查</text>
            </view>
          </view>
        </view>

        <!-- 未核查提示卡片 -->
        <view class="form-section not-inspected-section">
          <view class="not-inspected-content">
            <view class="not-inspected-icon-wrapper">
              <wd-icon name="warning" size="48rpx" color="#faad14" />
            </view>
            <view class="not-inspected-info">
              <text class="not-inspected-title">该泵房本次任务未进行核查</text>
              <text class="not-inspected-subtitle">请等积极参与核查工作</text>
              <view class="not-inspected-time">
                <wd-icon name="calendar" size="16rpx" color="#666" />
                <text class="time-text">任务创建时间：{{ endTime ? formatDateTime(endTime) : '暂无时间' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-section">
          <wd-button type="default" size="large" custom-class="back-button" @click="handleGoBack">
            <view class="button-content">
              <wd-icon name="arrow-left" size="16px" color="#fff" />
              <text class="button-text">返回</text>
            </view>
          </wd-button>
        </view>
      </div>

      <!-- 无需修改的最新数据页面 -->
      <div v-else-if="isLatestDataNoChange" class="latest-data-view">
        <!-- 页面头部 -->
        <view class="latest-header">
          <view class="header-content">
            <view class="header-icon latest">
              <wd-icon name="check-circle" size="32rpx" color="#1890ff" />
            </view>
            <view class="header-info">
              <text class="header-title">数据状态</text>
              <text class="header-subtitle">当前数据为最新状态</text>
            </view>
            <view class="status-badge latest">
              <wd-icon name="check" size="16rpx" color="#2b9cff" />
              <text class="status-text">最新</text>
            </view>
          </view>
        </view>

        <!-- 最新数据提示卡片 -->
        <view class="form-section latest-section">
          <view class="latest-content">
            <view class="latest-icon-wrapper">
              <wd-icon name="time" size="48rpx" color="#1890ff" />
            </view>
            <view class="latest-info">
              <text class="latest-title">当前时间点数据为最新数据</text>
              <text class="latest-subtitle">确定无需修改</text>
              <view class="latest-time">
                <wd-icon name="calendar" size="16rpx" color="#666" />
                <text class="time-text">数据时间：{{ endTime ? formatDateTime(endTime) : '暂无时间' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-section">
          <wd-button type="default" size="large" custom-class="back-button" @click="handleGoBack">
            <view class="button-content">
              <wd-icon name="arrow-left" size="16px" color="#fff" />
              <text class="button-text">返回</text>
            </view>
          </wd-button>
        </view>
      </div>

      <!-- 有数据的核查记录页面 -->
      <div v-else>
        <!-- 页面头部 -->
        <view class="record-header">
          <view class="header-content">
            <view class="header-icon">
              <wd-icon name="check-circle" size="32rpx" color="#fff" />
            </view>
            <view class="header-info">
              <text class="header-title">核查记录详情</text>
              <text class="header-subtitle">已完成的核查内容</text>
            </view>
            <view class="status-badge completed">
              <wd-icon name="check" size="16rpx" color="#61ca2a" />
              <text class="status-text">已完成</text>
            </view>
          </view>
        </view>

        <!-- 核查数据记录 -->
        <view class="form-section data-section" v-if="recordDetail.data && Object.keys(recordDetail.data).length > 0">
          <view class="section-header">
            <view class="section-icon data-icon">
              <wd-icon name="chart-pie" size="32rpx" color="#ffffff" />
            </view>
            <view class="section-title">
              <text class="title-text">核查数据记录</text>
              <text class="title-subtitle">已核查的数据项内容</text>
            </view>
          </view>
          <view class="data-content">
            <view class="data-grid">
              <view class="data-item" v-for="(value, key) in recordDetail.data" :key="key">
                <view class="data-label">
                  <wd-icon name="info" size="16rpx" color="#1890ff" />
                  <text class="label-text">{{ getFieldLabel(key) }}</text>
                </view>
                <view class="data-value" :class="getDataValueClass(key, value)">
                  <text class="value-text">{{ formatDataValue(key, value) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 节点核查记录 -->
        <view class="form-section nodes-section" v-if="recordDetail.node && recordDetail.node.length > 0">
          <view class="section-header">
            <view class="section-icon nodes-icon">
              <wd-icon name="list" size="32rpx" color="#65cb2e" />
            </view>
            <view class="section-title">
              <text class="title-text">节点核查记录</text>
              <text class="title-subtitle">共 {{ recordDetail.node.length }} 个节点的核查情况</text>
            </view>
          </view>

          <view class="nodes-content">
            <view class="node-record-card" v-for="(nodeItem, index) in recordDetail.node" :key="nodeItem.nodeCode || index">
              <!-- 节点头部信息 -->
              <view class="node-header">
                <view class="node-number-badge">{{ nodeItem.nodeCode }}</view>
                <view class="node-basic-info">
                  <text class="node-name">{{ nodeItem.nodeName }}</text>
                  <view class="node-status-info">
                    <view class="status-indicator" :class="getNodeStatusClass(nodeItem)">
                      <wd-icon :name="getNodeStatusIcon(nodeItem)" size="16rpx" :color="getNodeStatusColor(nodeItem)" />
                      <text class="status-label">{{ getNodeStatusText(nodeItem) }}</text>
                    </view>
                    <text class="completion-date" v-if="nodeItem.nodeData?.CompletionTime">{{ formatDateTime(nodeItem.nodeData.CompletionTime) }}</text>
                  </view>
                </view>
              </view>

              <!-- 节点详细信息 -->
              <view class="node-details">
                <!-- 基本信息 -->
                <view class="detail-section">
                  <view class="detail-row">
                    <view class="detail-label">
                      <wd-icon name="calendar" size="16rpx" color="#666" />
                      <text>完成时间</text>
                    </view>
                    <text class="detail-value">{{ formatDateTime(nodeItem.nodeData.CompletionTime) }}</text>
                  </view>
                </view>

                <!-- 备注信息 -->
                <view class="remark-section" v-if="nodeItem.nodeData.Remark">
                  <view class="remark-header">
                    <wd-icon name="edit" size="16rpx" color="#722ed1" />
                    <text class="remark-title">节点备注</text>
                  </view>
                  <view class="remark-content">
                    <text class="remark-text">{{ nodeItem.nodeData.Remark }}</text>
                  </view>
                </view>

                <!-- 必需文件 -->
                <view class="files-section" v-if="getRequiredFiles(nodeItem).length > 0">
                  <view class="files-header">
                    <wd-icon name="folder" size="16rpx" color="#1890ff" />
                    <text class="files-title">相关文件</text>
                    <view class="files-count-badge">{{ getRequiredFiles(nodeItem).length }}</view>
                  </view>
                  <view class="files-grid">
                    <view class="file-card" v-for="(file, fileIndex) in getRequiredFiles(nodeItem)" :key="file.FileCode || fileIndex">
                      <view class="file-icon-wrapper">
                        <view class="file-code">{{ file.FileCode }}</view>
                      </view>
                      <view class="file-info">
                        <text class="file-name">{{ file.FileName }}</text>
                      </view>
                      <view class="file-status" :class="{ 'status-uploaded': file.UploadedPath, 'status-not-uploaded': !file.UploadedPath }">
                        <wd-icon :name="file.UploadedPath ? 'check-circle' : 'close-circle'" size="12rpx" :color="file.UploadedPath ? '#52c41a' : '#ff4d4f'" />
                        <text class="file-status-text">{{ file.UploadedPath ? '已上传' : '未上传' }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-section">
          <wd-button type="default" size="large" custom-class="back-button" @click="handleGoBack">
            <view class="button-content">
              <wd-icon name="arrow-left" size="16px" color="#fff" />
              <text class="button-text">返回</text>
            </view>
          </wd-button>
        </view>
      </div>
    </div>

    <!-- 原有的编辑页面 -->
    <div v-else class="pump-house-inspect">
      <!-- 物业信息表单 -->
      <view class="form-section property-section">
        <view class="section-header">
          <view class="section-icon property-icon">
            <wd-icon name="home" size="32rpx" color="#ffffff" />
          </view>
          <view class="section-title">
            <text class="title-text">基础信息</text>
            <text class="title-subtitle">需核查的数据项</text>
          </view>
        </view>

        <view class="form-content">
          <wd-cell-group custom-class="form-group" border>
            <wd-input label="泵房名称" label-width="220rpx" show-word-limit prop="PumpHouseName" suffix-icon="warn-bold" clearable v-model="updataData.PumpHouseName" v-if="isExist('PumpHouseName')" placeholder="请输入泵房名称" />
            <wd-picker label="改造状态" placeholder="选择状态" label-width="220rpx" prop="RemouldState" v-model="updataData.RemouldState" v-if="isExist('RemouldState')" :columns="selectOption.GZ" />
            <wd-picker label="泵房批次" placeholder="选择批次" label-width="220rpx" prop="Batch" v-model="updataData.Batch" v-if="isExist('Batch')" :columns="selectOption.LX" />
            <wd-input label="小区地址" label-width="220rpx" show-word-limit prop="ResidentialAddress" suffix-icon="warn-bold" v-if="isExist('ResidentialAddress')" clearable v-model="updataData.ResidentialAddress" placeholder="请输入地址" />
            <wd-input
              label="加压供水户数"
              type="number"
              label-width="220rpx"
              show-word-limit
              prop="PressurizedHouseholds"
              v-if="isExist('PressurizedHouseholds')"
              suffix-icon="warn-bold"
              clearable
              v-model="updataData.PressurizedHouseholds"
              placeholder="请输入加压户数"
            />
            <wd-input
              label="小区建设时间"
              label-width="220rpx"
              show-word-limit
              prop="ConstructionTime"
              v-if="isExist('ConstructionTime')"
              suffix-icon="warn-bold"
              clearable
              v-model="updataData.ConstructionTime"
              placeholder="请输入建设时间"
            />
            <wd-input
              label="泵房管理状态"
              label-width="220rpx"
              show-word-limit
              prop="PumpRoomControlledState"
              v-if="isExist('PumpRoomControlledState')"
              suffix-icon="warn-bold"
              clearable
              v-model="updataData.PumpRoomControlledState"
              placeholder="请输入管理状态"
            />
            <wd-input label="物业单位" label-width="220rpx" show-word-limit prop="PropertyUnit" v-if="isExist('PropertyUnit')" suffix-icon="warn-bold" clearable v-model="updataData.PropertyUnit" placeholder="请输入物业管理单位" />
            <wd-input label="物业联系人" label-width="220rpx" show-word-limit prop="ContactPerson" v-if="isExist('ContactPerson')" suffix-icon="warn-bold" clearable v-model="updataData.ContactPerson" placeholder="请输入联系人" />
            <wd-input label="物业电话" label-width="220rpx" show-word-limit prop="PhoneNumber" v-if="isExist('PhoneNumber')" suffix-icon="warn-bold" clearable v-model="updataData.PhoneNumber" placeholder="请输入电话" />
            <wd-picker label="项目进展状态" placeholder="选择进展状态" label-width="250rpx" prop="ProgressStatus" v-if="isExist('ProgressStatus')" v-model="updataData.ProgressStatus" :columns="selectOption.ZT" />
            <wd-picker label="运营管理状态" placeholder="选择管理状态" label-width="220rpx" prop="Batch" v-if="isExist('OperationManagementState')" v-model="updataData.OperationManagementState" :columns="selectOption.YY" />
            <wd-input label="施工单位" label-width="250rpx" show-word-limit prop="ConstructionUnit" v-if="isExist('ConstructionUnit')" suffix-icon="warn-bold" clearable v-model="updataData.ConstructionUnit" placeholder="请输入施工单位" />
            <wd-input label="现场监管责任人" label-width="250rpx" show-word-limit prop="PersonInCharge" v-if="isExist('PersonInCharge')" suffix-icon="warn-bold" clearable v-model="updataData.PersonInCharge" placeholder="请输入责任人" />
            <wd-input
              label="临供停水事件数"
              label-width="250rpx"
              show-word-limit
              prop="TemporarySupplyEvents"
              v-if="isExist('TemporarySupplyEvents')"
              suffix-icon="warn-bold"
              clearable
              v-model="updataData.TemporarySupplyEvents"
              placeholder="请输入停水数"
            />
            <wd-calendar label="初步验收时间" label-width="250rpx" custom-class=" " v-model="updataData.AcceptanceTime" prop="AcceptanceTime" v-if="isExist('AcceptanceTime')" />
            <wd-textarea label="备注" auto-height type="textarea" v-model="updataData.Remark" :maxlength="200" show-word-limit placeholder="请输入备注信息" clearable prop="Remark" v-if="isExist('Remark')" />
            <wd-textarea
              label="小区地址"
              auto-height
              type="textarea"
              v-model="updataData.ResidentialAddress"
              v-if="isExist('ResidentialAddress')"
              :maxlength="200"
              show-word-limit
              placeholder="请输入地址信息"
              clearable
              prop="ResidentialAddress"
            />
            <wd-textarea
              label="泵房精确位置"
              auto-height
              type="textarea"
              v-model="updataData.AccuratePosition"
              v-if="isExist('AccuratePosition')"
              show-word-limit
              placeholder="泵房精确位置填写规则 如：xxx小区xxx栋地下二层"
              clearable
              prop="AccuratePosition"
            />
            <view class="image-upload-section" v-if="isExist('PumpHouseImg')">
              <view class="upload-title">泵房图片</view>
              <view class="upload-container">
                <UploadeImg :maxlength="3" :handleImg="({ data }) => data" :url="url" v-model="updataData.PumpHouseImg" />
              </view>
            </view>
          </wd-cell-group>
        </view>
      </view>

      <view class="form-section basic-section">
        <view class="section-header">
          <view class="section-icon basic-icon">
            <wd-icon name="setting" size="32rpx" color="#ffffff" />
          </view>
          <view class="section-title">
            <text class="title-text">节点信息</text>
            <text class="title-subtitle">泵房项目节点状态设置</text>
          </view>
        </view>

        <view class="form-content" style="padding: 24rpx 0">
          <!-- 节点列表折叠控制 -->
          <view class="nodes-collapse-header" @click="toggleNodesCollapse">
            <view class="collapse-header-content">
              <wd-icon name="list" size="16px" color="#722ed1" />
              <text class="collapse-title">节点列表</text>
              <view class="nodes-summary">
                <text class="summary-text">{{ getNodesSummary() }}</text>
              </view>
            </view>
            <view class="nodes-collapse-icon" :class="{ collapsed: isNodesCollapsed }">
              <wd-icon name="arrow-down" size="16px" color="#722ed1" />
            </view>
          </view>

          <view class="node-cards-container" :class="{ 'nodes-collapsed': isNodesCollapsed }">
            <template v-for="item in getDisplayNodes()" :key="item.nodeCode">
              <view
                class="node-card"
                :class="{
                  'node-completed': item.nodeData?.IsEnd,
                  'node-empty': !item.nodeData,
                  'node-preview': isNodesCollapsed
                }"
              >
                <!-- 卡片头部 -->
                <view class="node-card-header">
                  <view class="node-info">
                    <view class="node-number" :class="getNodeStatusClass(item)">{{ item.nodeCode }}</view>
                    <view class="node-content-wrapper">
                      <view class="node-title">{{ item.nodeName }}</view>
                      <view class="node-subtitle" v-if="item.nodeData">
                        <text class="completion-time">{{ formatDate(item.nodeData.CompletionTime) || '未设置完成时间' }}</text>
                      </view>
                    </view>
                  </view>
                  <view class="node-actions">
                    <view class="node-status" :class="getNodeStatusClass(item)">
                      <wd-icon :name="getNodeStatusIcon(item)" size="14px" :color="getNodeStatusColor(item)" />
                    </view>
                    <wd-switch v-if="item.nodeData && !isNodesCollapsed" v-model="item.nodeData.IsEnd" size="18px" @change="handleNodeStatusChange(item)" custom-class="compact-switch" />
                  </view>
                </view>

                <!-- 卡片内容 - 仅当有nodeData时显示 -->
                <view class="node-card-content" v-if="item.nodeData && !isNodesCollapsed">
                  <!-- 紧凑的编辑区域 -->
                  <view class="compact-fields">
                    <!-- 完成时间和备注在同一行 -->
                    <view class="field-row">
                      <view class="field-item flex-1">
                        <view class="field-label-compact">
                          <wd-icon name="calendar" size="12px" color="#666" />
                          <text>完成时间</text>
                        </view>
                        <wd-calendar v-model="item.nodeData.CompletionTime" custom-class="compact-calendar" @confirm="handleDateChange(item)" :disabled="!item.nodeData.IsEnd">
                          <view class="date-display-compact" :class="{ 'date-disabled': !item.nodeData.IsEnd }">
                            {{ formatDate(item.nodeData.CompletionTime) || '选择日期' }}
                          </view>
                        </wd-calendar>
                      </view>
                    </view>

                    <!-- 备注信息 -->
                    <view class="field-item" v-if="item.nodeData.IsEnd || item.nodeData.Remark">
                      <view class="field-label-compact">
                        <wd-icon name="edit" size="12px" color="#666" />
                        <text>备注</text>
                      </view>
                      <wd-textarea v-model="item.nodeData.Remark" placeholder="请输入备注信息" :maxlength="100" auto-height :disabled="!item.nodeData.IsEnd" @change="handleRemarkChange(item)" custom-class="compact-textarea" />
                    </view>
                  </view>

                  <!-- 必需文件列表 -->
                  <view class="required-files" v-if="getRequiredFiles(item).length > 0">
                    <view class="files-header" @click="toggleFilesCollapse(item)">
                      <wd-icon name="folder" size="12px" color="#722ed1" />
                      <text class="files-title">必需文件</text>
                      <view class="files-count">{{ getRequiredFiles(item).length }}</view>
                      <view class="collapse-icon" :class="{ collapsed: isFilesCollapsed(item) }">
                        <wd-icon name="arrow-down" size="12px" color="#722ed1" />
                      </view>
                    </view>
                    <view class="files-list" :class="{ 'files-collapsed': isFilesCollapsed(item) }">
                      <view class="file-item" v-for="(file, index) in getRequiredFiles(item)" :key="file.FileCode || index">
                        <view class="file-icon">{{ file.FileCode }}</view>
                        <text class="file-name" :class="{ 'file-uploaded': file.UploadedPath }">{{ file.FileName }}</text>
                        <view class="file-actions">
                          <view class="upload-status" v-if="file.UploadedPath">
                            <wd-icon name="check-circle" size="12px" color="#52c41a" />
                          </view>
                          <view class="upload-btn" @click="handleFileUpload(item, file)">
                            <wd-icon name="upload" size="12px" color="#722ed1" />
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>

                  <!-- 紧凑的元数据 -->
                  <view class="node-meta-compact" v-if="item.nodeData.UpdateTime || item.nodeData.UpdatePerson">
                    <text class="meta-text-compact">
                      {{ formatDateTime(item.nodeData.UpdateTime) }}
                      <text v-if="item.nodeData.UpdatePerson"> · {{ item.nodeData.UpdatePerson }}</text>
                    </text>
                  </view>
                </view>

                <!-- 空状态提示 -->
                <view class="node-empty-state" v-else>
                  <view class="empty-content">
                    <wd-icon name="info" size="24px" color="#d9d9d9" />
                    <text class="empty-text">暂无节点数据</text>
                    <view class="create-node-btn" @click="handleCreateNode(item)">
                      <wd-icon name="plus" size="14px" color="#722ed1" />
                      <text class="create-btn-text">创建节点</text>
                    </view>
                  </view>
                </view>
              </view>
            </template>
          </view>
        </view>
      </view>
    </div>

    <!-- 底部保存按钮 -->
    <view class="save-section" v-if="!isInspected">
      <view class="save-button-container">
        <wd-button type="primary" size="large" custom-class="save-button" @click="handleSaveData" :loading="saveLoading">
          <view class="button-content">
            <wd-icon name="check" size="16px" color="#ffffff" />
            <text class="button-text">保存核查数据</text>
          </view>
        </wd-button>
      </view>
    </view>
  </div>

  <wd-toast />
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import UploadeImg from '@/components/UploadeImg/index.vue'
import * as CommonApi from '@/services/model/common.js'
import { PumpHouseApi } from '@/services/model/pump.house'
import { getChangedObjects, getChangedFields } from '@/utils/util.js'
import { useToast } from 'wot-design-uni'
import moment from 'moment'

// 设置 moment 中文本地化
moment.locale('zh-cn')

const updataData = ref(null)
const updataPumpHouseNodeData = ref(null)
const toast = useToast()

// 文件折叠状态管理
const filesCollapseState = ref(new Map())

// 节点列表折叠状态
const isNodesCollapsed = ref(true)

// 保存状态
const saveLoading = ref(false)

onLoad(async ({ pumpRoomNumber, TaskID }) => {
  await getDetail(pumpRoomNumber, TaskID)
})

const url = computed(() => `https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${pumpHouseDetail.value.PumpRoomNumber}/img`)
const TaskContent = computed(() => taskInfo.value?.TaskContent?.split(','))

const taskInfo = ref({})
const pumpHouseDetail = ref(null)
const pumpHouseNode = ref(null)
const recordDetail = ref(null)
const endTime = ref(null)
async function getDetail(PumpRoomNumber, TaskID) {
  toast.loading('正在加载...')
  try {
    const { data } = await PumpHouseApi.tackDetaul(TaskID)
    taskInfo.value = data

    // 当 IsEnd 为 1 时，检查是否有核查记录
    if (data.IsEnd === 1) {
      const { data: data1 } = await PumpHouseApi.tackRecordDetail({ TaskID, PumpRoomNumber })
      if (data1) {
        // 有核查记录，解析并显示
        endTime.value = data1.CreateTime
        recordDetail.value = JSON.parse(data1.RecordContent)
      } else {
        // IsEnd 为 1 但没有核查记录，标记为未核查状态
        endTime.value = data.CreateTime
        recordDetail.value = null // 设置为 null 表示未核查
      }
      toast.close()
      return
    }

    // IsEnd 不为 1 时，仍然检查是否有核查记录
    const { data: data1 } = await PumpHouseApi.tackRecordDetail({ TaskID, PumpRoomNumber })
    if (data1) {
      endTime.value = data1.CreateTime
      recordDetail.value = JSON.parse(data1.RecordContent)
      toast.close()
      return
    }

    // 没有核查记录，加载编辑页面数据
    const res = await Promise.all([PumpHouseApi.detail(PumpRoomNumber), PumpHouseApi.nodelist(PumpRoomNumber)])
    pumpHouseDetail.value = res[0].data
    updataData.value = { ...res[0].data }
    pumpHouseNode.value = res[1].data
    updataPumpHouseNodeData.value = JSON.parse(JSON.stringify(res[1].data))
    getQueryDictionaries()

    toast.close()
  } catch (error) {
    toast.close()
    console.log(error)
    toast.error('数据加载失败')
  }
}

const selectOption = ref({})
async function getQueryDictionaries() {
  const renderToStream = await Promise.all([queryDictionaries('pumpHouseProjectStatus'), queryDictionaries('pumpHouseProjectBatch'), queryDictionaries('pumpHouseOperationState'), queryDictionaries('pumpHouseRemouldState')])
  const [pumpHouseProjectStatus, pumpHouseProjectBatch, pumpHouseOperationState, pumpHouseRemouldState] = renderToStream
  selectOption.value.ZT = pumpHouseProjectStatus.map((item) => ({ label: item.DictValue, value: item.DictValue })) //项目进展状态
  selectOption.value.LX = pumpHouseProjectBatch.map((item) => ({ label: item.DictValue, value: item.DictCode })) //泵房批次
  selectOption.value.YY = pumpHouseOperationState.map((item) => ({ label: item.DictValue, value: item.DictValue })) //运营管理状态
  selectOption.value.GZ = pumpHouseRemouldState.map((item) => ({ label: item.DictValue, value: item.DictValue })) //改造状态
}

async function queryDictionaries(type) {
  try {
    const { data } = await CommonApi.dictionaryLookup(type)
    return data
  } catch (error) {
    // message.error(error.message)
  }
}

function isExist(pr) {
  if (Array.isArray(pr)) {
    return pr.some((item) => TaskContent.value.includes(item))
  } else {
    return (TaskContent.value || []).includes(pr)
  }
}

// 节点状态相关方法
function getNodeStatusClass(item) {
  if (!item.nodeData) return 'status-empty'
  return item.nodeData.IsEnd ? 'status-completed' : 'status-pending'
}

function getNodeStatusIcon(item) {
  if (!item.nodeData) return 'info'
  return item.nodeData.IsEnd ? 'check-circle' : 'clock'
}

function getNodeStatusColor(item) {
  if (!item.nodeData) return '#d9d9d9'
  return item.nodeData.IsEnd ? '#52c41a' : '#faad14'
}

function getNodeStatusText(item) {
  if (!item.nodeData) return '暂无数据'
  return item.nodeData.IsEnd ? '已完成' : '未完成'
}

// 处理节点状态变更
function handleNodeStatusChange(item) {
  if (!item.nodeData.CompletionTime) {
    item.nodeData.CompletionTime = moment().toISOString()
  }
}

// 处理日期变更
function handleDateChange(item) {
  item.nodeData.CompletionTime = moment(item.nodeData.CompletionTime).toISOString()
  console.log('日期变更:', item.nodeName, item.nodeData.CompletionTime)
  // 这里可以添加保存到服务器的逻辑
  toast.success(`${item.nodeName} 完成时间已更新`)
}

// 处理备注变更
function handleRemarkChange(item) {
  console.log('备注变更:', item.nodeName, item.nodeData.Remark)
  // 这里可以添加保存到服务器的逻辑
}

// 格式化日期显示 - 使用 moment
function formatDate(dateStr) {
  if (!dateStr) return ''
  try {
    const momentDate = moment(dateStr)
    if (!momentDate.isValid()) return ''
    return momentDate.format('YYYY-MM-DD')
  } catch (error) {
    console.warn('formatDate error:', error)
    return ''
  }
}

// 格式化日期时间显示 - 使用 moment
function formatDateTime(dateStr) {
  if (!dateStr) return ''
  try {
    const momentDate = moment(dateStr)
    if (!momentDate.isValid()) return ''
    return momentDate.format('YYYY-MM-DD HH:mm')
  } catch (error) {
    console.warn('formatDateTime error:', error)
    return ''
  }
}

// 格式化相对时间显示 - 使用 moment
function formatRelativeTime(dateStr) {
  if (!dateStr) return ''
  try {
    const momentDate = moment(dateStr)
    if (!momentDate.isValid()) return ''
    return momentDate.fromNow()
  } catch (error) {
    console.warn('formatRelativeTime error:', error)
    return ''
  }
}

// 格式化完整日期时间显示 - 使用 moment
function formatFullDateTime(dateStr) {
  if (!dateStr) return ''
  try {
    const momentDate = moment(dateStr)
    if (!momentDate.isValid()) return ''
    return momentDate.format('YYYY年MM月DD日 HH:mm:ss')
  } catch (error) {
    console.warn('formatFullDateTime error:', error)
    return ''
  }
}

// 获取必需文件列表
function getRequiredFiles(item) {
  if (!item.nodeData || !item.nodeData.RequiredFiles) return []

  try {
    const files = JSON.parse(item.nodeData.RequiredFiles)
    return Array.isArray(files) ? files : []
  } catch (error) {
    console.warn('解析RequiredFiles失败:', error)
    return []
  }
}

// 文件折叠状态管理
function getFilesCollapseKey(item) {
  return `${item.nodeCode}_${item.nodeName}`
}

function isFilesCollapsed(item) {
  const key = getFilesCollapseKey(item)
  // 默认折叠状态为true
  return filesCollapseState.value.get(key) !== false
}

function toggleFilesCollapse(item) {
  const key = getFilesCollapseKey(item)
  const currentState = filesCollapseState.value.get(key)
  // 如果当前是undefined（默认折叠），则设置为false（展开）
  // 如果当前是false（展开），则设置为true（折叠）
  // 如果当前是true（折叠），则设置为false（展开）
  filesCollapseState.value.set(key, currentState === false ? true : false)
}

// 处理文件上传
function handleFileUpload(item, file) {
  console.log('上传文件:', item.nodeName, file.FileName)
  toast.info(`准备上传文件: ${file.FileName}`)

  // 这里可以添加实际的文件上传逻辑
  // 例如调用uni.chooseFile或其他文件选择API

  // 模拟上传成功（实际项目中应该调用真实的上传接口）
  setTimeout(() => {
    // 模拟设置上传路径
    file.UploadedPath = `/uploads/${Date.now()}_${file.FileName}`
    toast.success(`${file.FileName} 上传成功`)
  }, 1000)
}

// 节点列表折叠管理
function toggleNodesCollapse() {
  isNodesCollapsed.value = !isNodesCollapsed.value
}

// 获取要显示的节点列表
function getDisplayNodes() {
  if (!updataPumpHouseNodeData.value) return []

  if (isNodesCollapsed.value) {
    // 折叠状态下，只显示最后一个IsEnd为true的节点
    const completedNodes = updataPumpHouseNodeData.value.filter((item) => item.nodeData?.IsEnd)
    if (completedNodes.length > 0) {
      // 返回最后一个完成的节点，并确保它是只读的
      const lastCompleted = { ...completedNodes[completedNodes.length - 1] }
      return [lastCompleted]
    }
    // 如果没有完成的节点，显示第一个有数据的节点
    const nodesWithData = updataPumpHouseNodeData.value.filter((item) => item.nodeData)
    if (nodesWithData.length > 0) {
      return [{ ...nodesWithData[0] }]
    }
    // 如果都没有数据，显示第一个节点
    return updataPumpHouseNodeData.value.slice(0, 1)
  }

  return updataPumpHouseNodeData.value
}

// 获取节点摘要信息
function getNodesSummary() {
  if (!updataPumpHouseNodeData.value) return '暂无数据'

  const total = updataPumpHouseNodeData.value.length
  const completed = updataPumpHouseNodeData.value.filter((item) => item.nodeData?.IsEnd).length
  const hasData = updataPumpHouseNodeData.value.filter((item) => item.nodeData).length

  return `${completed}/${hasData} 已完成，共 ${total} 个节点`
}

// 创建节点
function handleCreateNode(item) {
  console.log('创建节点:', item.nodeName)

  // 创建新的节点数据
  const newNodeData = {
    Id: null,
    PumpRoomNumber: pumpHouseDetail.value?.PumpRoomNumber || '',
    IsEnd: false,
    Node: item.nodeCode,
    CompletionTime: null,
    Remark: '',
    UpdateTime: moment().toISOString(),
    UpdatePerson: '当前用户', // 这里应该从用户信息中获取
    RequiredFiles: null
  }

  // 更新节点数据
  item.nodeData = newNodeData

  toast.success(`${item.nodeName} 节点已创建`)
}

// 判断是否进入核查记录展示页面（IsEnd 为 1 或有核查记录）
const isInspected = computed(() => {
  return taskInfo.value?.IsEnd === 1 || !!recordDetail.value
})

// 判断是否为未核查状态（IsEnd 为 1 且没有核查记录）
const isNotInspected = computed(() => {
  return taskInfo.value?.IsEnd === 1 && recordDetail.value === null
})

// 判断是否为最新数据无需修改的情况
const isLatestDataNoChange = computed(() => {
  // 只有在已核查的情况下才判断是否为最新数据
  if (!isInspected.value || !recordDetail.value) return false

  // 检查 data 是否为空或者没有有效数据
  const hasData = recordDetail.value.data && Object.keys(recordDetail.value.data).length > 0

  // 检查 node 是否为空数组
  const hasNodes = recordDetail.value.node && recordDetail.value.node.length > 0

  // 当 data 和 node 都为空时，显示最新数据页面
  return !hasData && !hasNodes
})

// 获取进展状态样式类
function getProgressStatusClass(status) {
  switch (status) {
    case '正常':
      return 'status-normal'
    case '滞后':
      return 'status-delayed'
    case '超前':
      return 'status-ahead'
    default:
      return 'status-default'
  }
}

// 获取字段标签名称
function getFieldLabel(key) {
  const fieldLabels = {
    ProgressStatus: '项目进展状态',
    PumpHouseName: '泵房名称',
    RemouldState: '改造状态',
    Batch: '泵房批次',
    ResidentialAddress: '小区地址',
    PressurizedHouseholds: '加压供水户数',
    ConstructionTime: '小区建设时间',
    PumpRoomControlledState: '泵房管理状态',
    PropertyUnit: '物业单位',
    ContactPerson: '物业联系人',
    PhoneNumber: '物业电话',
    OperationManagementState: '运营管理状态',
    ConstructionUnit: '施工单位',
    PersonInCharge: '现场监管责任人',
    TemporarySupplyEvents: '临供停水事件数',
    AcceptanceTime: '初步验收时间',
    Remark: '备注',
    AccuratePosition: '泵房精确位置'
  }
  return fieldLabels[key] || key
}

// 格式化数据值显示
function formatDataValue(key, value) {
  if (value === null || value === undefined || value === '') {
    return '暂无数据'
  }

  // 时间相关字段的格式化
  const timeFields = ['AcceptanceTime', 'ConstructionTime', 'CreateTime', 'UpdateTime', 'CompletionTime']
  if (timeFields.includes(key) && value) {
    // 如果是时间字段，使用 moment 进行格式化
    try {
      const momentDate = moment(value)
      if (momentDate.isValid()) {
        // 根据字段类型选择不同的格式
        if (key === 'AcceptanceTime' || key === 'ConstructionTime') {
          return momentDate.format('YYYY-MM-DD')
        } else {
          return momentDate.format('YYYY-MM-DD HH:mm')
        }
      }
    } catch (error) {
      console.warn('formatDataValue time error:', error)
    }
  }

  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  if (typeof value === 'number') {
    return value.toString()
  }

  return value.toString()
}

// 获取数据值的样式类
function getDataValueClass(key, value) {
  if (value === null || value === undefined || value === '') {
    return 'value-empty'
  }

  if (key === 'ProgressStatus') {
    return getProgressStatusClass(value)
  }

  return 'value-normal'
}

// 返回按钮处理
function handleGoBack() {
  uni.navigateBack()
}

// 保存数据
async function handleSaveData() {
  if (saveLoading.value) return

  saveLoading.value = true
  toast.loading('正在保存...')

  const changedFields = getChangedFields(pumpHouseDetail.value, updataData.value)
  const changedNodes = getChangedObjects(pumpHouseNode.value, updataPumpHouseNodeData.value)

  try {
    await PumpHouseApi.update(updataData.value)
    await Promise.all(changedNodes.map((item) => PumpHouseApi.updateNode(item.nodeData)))
    await PumpHouseApi.tackRecord({
      TaskID: taskInfo.value.TaskID,
      PumpRoomNumber: pumpHouseDetail.value.PumpRoomNumber,
      RecordContent: JSON.stringify({ data: changedFields, node: changedNodes })
    })
    await getDetail(pumpHouseDetail.value.PumpRoomNumber, taskInfo.value.TaskID)

    toast.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    toast.error('保存失败，请重试')
  } finally {
    saveLoading.value = false
    toast.close()
  }
}
</script>

<style lang="less" scoped>
/* 核查记录展示页面样式 */
.inspection-record-view {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #f8faff 0%, #ffffff 100%);
}

/* 记录页面头部 */
.record-header {
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  margin-bottom: 24rpx;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.header-icon {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}

.header-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);

  &.completed {
    background: rgba(255, 255, 255, 0.9);
  }

  &.latest {
    background: rgba(255, 255, 255, 0.9);
  }
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #52c41a;

  .status-badge.latest & {
    color: #1890ff;
  }
}

/* 未核查状态页面样式 */
.not-inspected-view {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  background: linear-gradient(180deg, #fff7e6 0%, #ffffff 100%);
}

.not-inspected-header {
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
  margin-bottom: 24rpx;
}

.header-icon.not-inspected {
  background: rgba(255, 255, 255, 0.2);
}

.status-badge.not-inspected {
  background: rgba(255, 255, 255, 0.9);

  .status-text {
    color: #faad14;
  }
}

.not-inspected-section {
  margin: 0 24rpx 24rpx;
}

.not-inspected-content {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 40rpx 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fff7e6 100%);
  border-radius: 20rpx;
  border: 2rpx solid #ffd591;
  box-shadow: 0 8rpx 24rpx rgba(250, 173, 20, 0.1);
}

.not-inspected-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(250, 173, 20, 0.2);
}

.not-inspected-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.not-inspected-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #faad14;
  line-height: 1.3;
}

.not-inspected-subtitle {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.not-inspected-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(250, 173, 20, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(250, 173, 20, 0.1);
}

/* 最新数据页面样式 */
.latest-data-view {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  background: linear-gradient(180deg, #e6f7ff 0%, #ffffff 100%);
}

.latest-header {
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  margin-bottom: 24rpx;
}

.header-icon.latest {
  background: rgba(255, 255, 255, 0.2);
}

.latest-section {
  margin: 0 24rpx 24rpx;
}

.latest-content {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 40rpx 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  border-radius: 20rpx;
  border: 2rpx solid #91d5ff;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.1);
}

.latest-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.2);
}

.latest-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.latest-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
  line-height: 1.3;
}

.latest-subtitle {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}

.latest-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(24, 144, 255, 0.1);
}

.time-text {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

/* 状态内容区域 */
.status-content {
  padding: 24rpx;
}

.progress-status-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #fff7e6 0%, #ffffff 100%);
  border-radius: 16rpx;
  border: 1rpx solid #ffd591;
}

.progress-icon {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #666666;
}

.progress-value {
  font-size: 28rpx;
  font-weight: 600;

  &.status-normal {
    color: #52c41a;
  }

  &.status-delayed {
    color: #fa8c16;
  }

  &.status-ahead {
    color: #1890ff;
  }

  &.status-default {
    color: #666666;
  }
}

/* 数据记录区域样式 */
.data-content {
  padding: 24rpx;
}

.data-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.data-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.02) 0%, rgba(64, 169, 255, 0.01) 100%);
  border: 1rpx solid rgba(24, 144, 255, 0.1);
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.04) 0%, rgba(64, 169, 255, 0.02) 100%);
    border-color: rgba(24, 144, 255, 0.2);
    transform: translateY(-1rpx);
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);
  }
}

.data-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  min-width: 200rpx;
  flex-shrink: 0;
}

.label-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #1890ff;
  line-height: 1.4;
}

.data-value {
  flex: 1;
  text-align: right;
  min-width: 0;
}

.value-text {
  font-size: 26rpx;
  font-weight: 500;
  line-height: 1.5;
  word-break: break-all;

  .value-normal & {
    color: #333333;
  }

  .value-empty & {
    color: #999999;
    font-style: italic;
  }

  .status-normal & {
    color: #52c41a;
  }

  .status-delayed & {
    color: #fa8c16;
  }

  .status-ahead & {
    color: #1890ff;
  }

  .status-default & {
    color: #666666;
  }
}

.data-icon {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

/* 节点记录卡片 */
.nodes-content {
  padding: 24rpx;
}

.node-record-card {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(82, 196, 26, 0.1);
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
    transform: translateY(-2rpx);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  }
}

.node-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
  border-bottom: 1rpx solid #f0f0f0;
}

.node-number-badge {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}

.node-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.node-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.node-status-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;

  &.status-completed {
    background: rgba(82, 196, 26, 0.1);

    .status-label {
      color: #52c41a;
    }
  }

  &.status-pending {
    background: rgba(250, 173, 20, 0.1);

    .status-label {
      color: #faad14;
    }
  }

  &.status-empty {
    background: rgba(156, 163, 175, 0.1);

    .status-label {
      color: #9ca3af;
    }
  }

  // 保持向后兼容
  &.completed {
    background: rgba(82, 196, 26, 0.1);

    .status-label {
      color: #52c41a;
    }
  }
}

.status-indicator .status-label {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1.2;
}

.completion-date {
  font-size: 22rpx;
  color: #666666;
  background: rgba(0, 0, 0, 0.04);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 节点详细信息 */
.node-details {
  padding: 24rpx;
}

.detail-section {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.01) 100%);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.02) 100%);
    border-color: rgba(0, 0, 0, 0.08);
  }
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.detail-value {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
}

/* 备注区域 */
.remark-section {
  margin-bottom: 20rpx;
}

.remark-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.remark-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #722ed1;
}

.remark-content {
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(114, 46, 209, 0.02) 0%, rgba(146, 84, 222, 0.01) 100%);
  border: 1rpx solid rgba(114, 46, 209, 0.1);
  border-radius: 12rpx;
}

.remark-text {
  font-size: 24rpx;
  color: #333333;
  line-height: 1.6;
}

/* 文件区域 */
.files-section {
  margin-bottom: 20rpx;
}

.files-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.files-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1890ff;
  flex: 1;
}

.files-count-badge {
  background: #1890ff;
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}

.files-grid {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.file-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.02) 0%, rgba(64, 169, 255, 0.01) 100%);
  border: 1rpx solid rgba(24, 144, 255, 0.1);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.04) 0%, rgba(64, 169, 255, 0.02) 100%);
    border-color: rgba(24, 144, 255, 0.2);
    transform: translateY(-1rpx);
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);
  }
}

.file-icon-wrapper {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.2);
}

.file-code {
  color: #ffffff;
  font-size: 18rpx;
  font-weight: 600;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.file-name {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  line-height: 1.4;
}

.file-status {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.file-status-text {
  font-size: 20rpx;
  font-weight: 500;
}

.file-status {
  &.status-uploaded .file-status-text {
    color: #52c41a;
  }

  &.status-not-uploaded .file-status-text {
    color: #ff4d4f;
  }
}

/* 操作按钮区域 */
.action-section {
  padding: 24rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.95) 20%, #ffffff 100%);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

:deep(.back-button) {
  width: 100% !important;
  height: 88rpx !important;
  border-radius: 20rpx !important;
  background: linear-gradient(135deg, #2987f3 0%, #256ece 100%) !important;
  border: 1rpx solid #d9d9d9 !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08) !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
    transition: left 0.6s ease;
  }

  &:active {
    transform: scale(0.98) !important;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12) !important;
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%) !important;
  }

  &:active::before {
    left: 100%;
  }
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.button-text {
  color: #666666;
  font-size: 28rpx;
  font-weight: 500;
}

.pump-house-inspect {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
}

/* 页面头部装饰 */
.page-header {
  position: relative;
  height: 120rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.header-decoration {
  position: relative;
  width: 100%;
  height: 100%;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(64, 169, 255, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 80rpx;
  height: 80rpx;
  top: 20rpx;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 60rpx;
  height: 60rpx;
  top: 10rpx;
  right: 20%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 40rpx;
  right: 10%;
  animation-delay: 4s;
}

/* 表单分组样式 */
.form-section {
  // margin-bottom: 32rpx;
  background: #ffffff;
  // border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  animation: fadeInUp 0.6s ease-out;

  &.basic-section {
    animation-delay: 0.1s;
  }

  &.property-section {
    animation-delay: 0.2s;
  }

  &.project-section {
    animation-delay: 0.3s;
  }

  &.location-section {
    animation-delay: 0.4s;
  }

  &.node-section {
    animation-delay: 0.5s;
  }
}

/* 分组头部样式 */
.section-header {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  border-bottom: 1rpx solid #f0f0f0;
}

.section-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.basic-icon {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.property-icon {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.project-icon {
  background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
}

.location-icon {
  background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%);
}

.node-icon {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
}

.section-title {
  flex: 1;
}

.title-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.title-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

/* 表单内容样式 */
.form-content {
  padding: 0;
}

:deep(.form-group) {
  border-radius: 0 !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 位置信息特殊样式 */
.location-update-btn {
  margin: 24rpx 32rpx;
  padding: 20rpx 32rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 3rpx 12rpx rgba(82, 196, 26, 0.4);
  }
}

.update-btn-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
}

.image-upload-section {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.upload-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.upload-container {
  border-radius: 12rpx;
  overflow: hidden;
}

/* 节点信息现代化样式 */
.node-tabs-container {
  padding: 0;
}

:deep(.modern-node-tabs) {
  .wd-tabs__nav {
    background: #f8faff;
    padding: 16rpx 32rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .wd-tab {
    margin-right: 24rpx;
    margin-bottom: 16rpx;
    padding: 12rpx 20rpx;
    border-radius: 12rpx;
    background: #ffffff;
    border: 1rpx solid #e8e8e8;
    transition: all 0.3s ease;
    font-size: 28rpx;

    &.is-active {
      background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
      border-color: #722ed1;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

.node-content {
  padding: 32rpx;
  min-height: 300rpx;
  background: #ffffff;
}

.node-detail {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 状态区域 */
.status-section {
  padding: 24rpx;
  background: #f8faff;
  border-radius: 12rpx;
  border: 1rpx solid #e8f4ff;
}

.status-section .status-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

:deep(.status-radio) {
  .wd-radio {
    margin-right: 16rpx;

    &.is-checked {
      background: #722ed1;
      border-color: #722ed1;
      color: #ffffff;
    }
  }
}

/* 时间和备注区域 */
.time-section,
.remark-section {
  margin-bottom: 8rpx;
}

/* 文件上传区域 */
.file-section {
  margin-top: 8rpx;
  padding: 24rpx;
  background: #f8faff;
  border-radius: 12rpx;
  border: 1rpx solid #e8f4ff;
}

.file-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #e0e6ff;
  margin-bottom: 16rpx;
}

.file-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.file-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;

  &.file-completed {
    color: #52c41a;
  }
}

.file-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #52c41a;
}

.file-action {
  margin-left: 16rpx;
}

.upload-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(114, 46, 209, 0.4);
  }
}

/* 简化的上传按钮样式，确保app端兼容性 */
.upload-btn-simple {
  width: 80rpx;
  height: 80rpx;
  background-color: #722ed1;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 240rpx;
  padding: 40rpx 20rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.add-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.2);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(114, 46, 209, 0.3);
  }
}

.add-text {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 500;
}

/* 提交按钮区域样式 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.95) 20%, #ffffff 100%);
  padding: 24rpx 24rpx 40rpx;
  z-index: 10;
  backdrop-filter: blur(10rpx);
}

.submit-button-container {
  width: 100%;
  max-width: 600rpx;
  margin: 0 auto;
}

:deep(.submit-button) {
  width: 100% !important;
  height: 96rpx !important;
  border-radius: 24rpx !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  border: none !important;
  box-shadow: 0 12rpx 32rpx rgba(24, 144, 255, 0.3) !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
  }

  &:active {
    transform: scale(0.98) !important;
    box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.4) !important;
  }

  &:not(:disabled):active::before {
    left: 100%;
  }

  &:disabled {
    background: linear-gradient(135deg, #d9d9d9 0%, #f0f0f0 100%) !important;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
    transform: none !important;
  }
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.button-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
}

.updateIcon {
  top: 16rpx;
  right: 26rpx;
  padding: 4rpx 18rpx;
  background: #15a131;
  border-radius: 12rpx;
}

:deep(.wd-tabs__map-nav-btn) {
  width: auto;
  height: auto;
  padding: 8rpx 18rpx;
  margin-bottom: 10rpx;
  line-height: normal;
}

/* 优化后的上传模态框样式 */
.upload-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.upload-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4rpx);
}

.upload-modal-content {
  position: relative;
  background: #ffffff;
  border-radius: 32rpx;
  padding: 48rpx 40rpx;
  margin: 0 40rpx;
  max-width: 500rpx;
  width: 100%;
  box-shadow: 0 24rpx 64rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.upload-icon {
  width: 96rpx;
  height: 96rpx;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  animation: pulse 2s infinite;
}

.upload-subtitle {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.progress-container {
  width: 100%;
  position: relative;
}

.progress-text {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
  margin-top: 16rpx;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 动画效果 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 节点列表折叠控制 */
.nodes-collapse-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  margin: 0 20rpx 16rpx;
  background: linear-gradient(135deg, rgba(114, 46, 209, 0.05) 0%, rgba(146, 84, 222, 0.02) 100%);
  border: 1rpx solid rgba(114, 46, 209, 0.1);
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.collapse-header-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.collapse-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #722ed1;
}

.nodes-summary {
  margin-left: 16rpx;
}

.summary-text {
  font-size: 24rpx;
  color: #666666;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.nodes-collapse-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;

  &.collapsed {
    transform: rotate(-90deg);
  }
}

/* 节点卡片样式 - 优化版 */
.node-cards-container {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: 10000rpx;

  &.nodes-collapsed {
    max-height: 120rpx;
  }
}

.node-card {
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0 20rpx;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #d9d9d9 0%, #f0f0f0 100%);
    transition: all 0.3s ease;
  }

  &.node-completed {
    border: 1rpx solid rgba(82, 196, 26, 0.2);
    background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);

    &::before {
      background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
    }
  }

  &.node-empty {
    background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);

    &::before {
      background: linear-gradient(90deg, #d9d9d9 0%, #bfbfbf 100%);
    }
  }

  &.node-preview {
    cursor: default;
    pointer-events: none;
  }
}

.node-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx 16rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.node-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.node-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 700;
  transition: all 0.3s ease;

  &.status-completed {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  }

  &.status-pending {
    background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
    box-shadow: 0 4rpx 12rpx rgba(250, 173, 20, 0.3);
  }

  &.status-empty {
    background: linear-gradient(135deg, #d9d9d9 0%, #bfbfbf 100%);
    box-shadow: 0 4rpx 12rpx rgba(217, 217, 217, 0.3);
  }
}

.node-content-wrapper {
  flex: 1;
  min-width: 0;
}

.node-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.3;
  margin-bottom: 4rpx;
}

.node-subtitle {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.completion-time {
  font-size: 22rpx;
  color: #666666;
  background: rgba(0, 0, 0, 0.04);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.node-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.node-status {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.status-completed {
    background: rgba(82, 196, 26, 0.1);
  }

  &.status-pending {
    background: rgba(250, 173, 20, 0.1);
  }

  &.status-empty {
    background: rgba(217, 217, 217, 0.1);
  }
}

.node-card-content {
  padding: 20rpx 24rpx 24rpx;
}

.compact-fields {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.field-row {
  display: flex;
  gap: 16rpx;
}

.field-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;

  &.flex-1 {
    flex: 1;
  }
}

.field-label-compact {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #666666;
}

.date-display-compact {
  padding: 12rpx 16rpx;
  background: rgba(0, 0, 0, 0.02);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #333333;
  cursor: pointer;
  transition: all 0.3s ease;

  &.date-disabled {
    background: rgba(0, 0, 0, 0.02);
    color: #999999;
    cursor: not-allowed;
    border-color: rgba(0, 0, 0, 0.04);
  }
}

/* 必需文件样式 */
.required-files {
  margin-top: 16rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(114, 46, 209, 0.02) 0%, rgba(146, 84, 222, 0.01) 100%);
  border: 1rpx solid rgba(114, 46, 209, 0.1);
  border-radius: 12rpx;
}

.files-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
  cursor: pointer;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.files-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #722ed1;
  flex: 1;
}

.files-count {
  background: #722ed1;
  color: #ffffff;
  font-size: 20rpx;
  font-weight: 600;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
}

.collapse-icon {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;

  &.collapsed {
    transform: rotate(-90deg);
  }
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: 1000rpx;

  &.files-collapsed {
    max-height: 0;
    margin-top: 0;
    gap: 0;
  }
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 10rpx 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.file-icon {
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  color: #ffffff;
  border-radius: 6rpx;
  font-size: 18rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(114, 46, 209, 0.2);
  min-width: 28rpx;
}

.file-name {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.4;
  flex: 1;
  transition: color 0.3s ease;

  &.file-uploaded {
    color: #1890ff;
    font-weight: 500;
  }
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.upload-status {
  width: 20rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(82, 196, 26, 0.1);
  border-radius: 50%;
}

.upload-btn {
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(114, 46, 209, 0.1);
  border-radius: 6rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.node-meta-compact {
  margin-top: 16rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.meta-text-compact {
  font-size: 20rpx;
  color: #999999;
  line-height: 1.4;
}

.node-empty-state {
  padding: 40rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.empty-text {
  font-size: 24rpx;
  color: #999999;
}

.create-node-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  color: #ffffff;
  border-radius: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.3);

  &:active {
    transform: translateY(0);
    box-shadow: 0 2rpx 8rpx rgba(114, 46, 209, 0.3);
  }
}

.create-btn-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 保存按钮区域 */
.save-section {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.95) 20%, #ffffff 100%);
  padding: 24rpx 24rpx 40rpx;

  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.save-button-container {
  width: 100%;
  max-width: 600rpx;
  margin: 0 auto;
}

:deep(.save-button) {
  width: 100% !important;
  height: 96rpx !important;
  border-radius: 24rpx !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  border: none !important;
  box-shadow: 0 12rpx 32rpx rgba(24, 144, 255, 0.3) !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
  }

  &:active {
    transform: scale(0.98) !important;
    box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.4) !important;
  }

  &:not(:disabled):active::before {
    left: 100%;
  }

  &:disabled {
    background: linear-gradient(135deg, #d9d9d9 0%, #f0f0f0 100%) !important;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
    transform: none !important;
  }
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.button-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
}

/* 自定义组件样式 */
:deep(.compact-calendar) {
  .wd-calendar__input {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
  }
}

:deep(.compact-textarea) {
  .wd-textarea__textarea {
    padding: 12rpx 16rpx !important;
    background: rgba(0, 0, 0, 0.02) !important;
    border: 1rpx solid rgba(0, 0, 0, 0.06) !important;
    border-radius: 12rpx !important;
    font-size: 24rpx !important;
  }
}

:deep(.compact-switch) {
  transform: scale(0.9);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .pump-house-update {
    background: linear-gradient(180deg, #1a1a1a 0%, #2a2a2a 100%);
  }

  .form-section {
    background: #333333;
    border-color: #444444;
  }

  .section-header {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
    border-bottom-color: #444444;
  }

  .title-text {
    color: #ffffff;
  }

  .title-subtitle {
    color: #cccccc;
  }

  .image-upload-section {
    background: #2a2a2a;
    border-top-color: #444444;
  }

  .node-content {
    background: #333333;
  }

  .status-section,
  .file-section {
    background: #2a2a2a;
    border-color: #444444;
  }

  .status-section .status-label,
  .file-title {
    color: #ffffff;
  }

  .status-indicator {
    &.status-completed .status-label,
    &.completed .status-label {
      color: #52c41a;
    }

    &.status-pending .status-label {
      color: #faad14;
    }

    &.status-empty .status-label {
      color: #9ca3af;
    }
  }

  /* 数据记录区域深色模式 */
  .data-item {
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(64, 169, 255, 0.05) 100%);
    border-color: rgba(24, 144, 255, 0.2);

    &:hover {
      background: linear-gradient(135deg, rgba(24, 144, 255, 0.15) 0%, rgba(64, 169, 255, 0.08) 100%);
      border-color: rgba(24, 144, 255, 0.3);
    }
  }

  .label-text {
    color: #40a9ff;
  }

  .value-normal .value-text {
    color: #ffffff;
  }

  .value-empty .value-text {
    color: #cccccc;
  }

  .file-header {
    border-bottom-color: #444444;
  }

  .file-item {
    background: #333333;
    border-color: #444444;
  }

  .file-name {
    color: #ffffff;

    &.file-completed {
      color: #52c41a;
    }
  }

  .empty-text {
    color: #cccccc;
  }

  /* 节点卡片深色模式 */
  .node-card {
    background: #333333;
    border-color: #444444;

    &::before {
      background: linear-gradient(90deg, #444444 0%, #555555 100%);
    }

    &.node-completed {
      background: linear-gradient(135deg, #1f2937 0%, #333333 100%);
      border-color: rgba(82, 196, 26, 0.3);

      &::before {
        background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
      }
    }

    &.node-empty {
      background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
    }
  }

  .node-card-header {
    background: rgba(42, 42, 42, 0.8);
  }

  .node-title {
    color: #ffffff;
  }

  .completion-time {
    background: rgba(255, 255, 255, 0.1);
    color: #cccccc;
  }

  .field-label-compact {
    color: #cccccc;
  }

  .date-display-compact {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;

    &.date-disabled {
      background: rgba(255, 255, 255, 0.02);
      color: #666666;
      border-color: rgba(255, 255, 255, 0.05);
    }
  }

  .required-files {
    background: linear-gradient(135deg, rgba(114, 46, 209, 0.1) 0%, rgba(146, 84, 222, 0.05) 100%);
    border-color: rgba(114, 46, 209, 0.2);
  }

  .file-item {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .file-name {
    color: #cccccc;

    &.file-uploaded {
      color: #40a9ff;
    }
  }

  .upload-btn {
    background: rgba(114, 46, 209, 0.2);
  }

  .node-meta-compact {
    border-top-color: #444444;
  }

  .meta-text-compact {
    color: #cccccc;
  }

  .empty-text {
    color: #cccccc;
  }

  /* 节点列表折叠控制深色模式 */
  .nodes-collapse-header {
    background: linear-gradient(135deg, rgba(114, 46, 209, 0.1) 0%, rgba(146, 84, 222, 0.05) 100%);
    border-color: rgba(114, 46, 209, 0.2);
  }

  .collapse-title {
    color: #9254de;
  }

  .summary-text {
    background: rgba(255, 255, 255, 0.1);
    color: #cccccc;
  }

  .create-node-btn {
    background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
    box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.4);
  }

  .create-btn-text {
    color: #ffffff;
  }

  /* 保存按钮深色模式 */
  .save-section {
    background: linear-gradient(180deg, rgba(42, 42, 42, 0) 0%, rgba(42, 42, 42, 0.95) 20%, #2a2a2a 100%);
    border-top-color: #444444;
  }
}
</style>

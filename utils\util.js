export function getChangedObjects(originalArr, updatedArr) {
  return updatedArr.filter((item, index) => {
    if (JSON.stringify(item.nodeData) !== JSON.stringify(originalArr[index].nodeData)) {
      console.log(JSON.stringify(originalArr[index].nodeData))
      console.log(JSON.stringify(item.nodeData))
    }
    return JSON.stringify(item.nodeData) !== JSON.stringify(originalArr[index].nodeData)
  })

  // // 假设每个对象都有唯一 id 字段
  // return updatedArr.filter((updatedObj) => {
  //   const originalObj = originalArr.find((o) => o.nodeCode === updatedObj.nodeCode)
  //   // 如果原始数组没有该对象，或者对象内容有变化，则认为有变化
  //   return !originalObj || JSON.stringify(originalObj) !== JSON.stringify(updatedObj)
  // })
}

// 对比两个对象，返回变化的部分
export function getChangedFields(originalObj, updatedObj) {
  const changed = {}
  for (const key in updatedObj) {
    if (updatedObj.hasOwnProperty(key)) {
      // 只比较原始对象有的字段
      if (originalObj[key] !== updatedObj[key]) {
        changed[key] = updatedObj[key]
      }
    }
  }
  return changed
}
